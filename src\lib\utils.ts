import { clsx, type ClassValue } from "clsx";
import { getAuth } from "firebase/auth";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}
export function getLocation(
  attributes: { type: string; key: string; value: string }[] | undefined
): string | undefined {
  const locationAttr = attributes?.find((attr) => attr.key === "location");
  return locationAttr?.value;
}

export function getFormattedTime(isoDate: string): string {
  const date = new Date(isoDate);
  const day = date.getUTCDate();
  const month = date.toLocaleString("default", { month: "long", timeZone: "UTC" });
  const year = date.getUTCFullYear();

  const formatted = `${day} ${month} ${year}`;
  return formatted;
}

export async function getIdToken() {
   const auth = getAuth();
   const user = auth.currentUser;

  if (!user) {
    throw new Error("User not logged in");
  }
  const idToken = await user.getIdToken();
  return idToken
}



export const BASE_URL = "https://amuzn-webapp-dev.vercel.app/"; // DEV
//export const BASE_URL ='https://www.amuzn.app/'; // PROD
export const GATE_URL = `/api/gate`;

